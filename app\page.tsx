"use client";

import {
  ChampionsIcon,
  SkinsIcon,
} from "@/components/navigation/navigation-data";
import SharedLayout from "@/components/shared-layout";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { DidYouMean } from "@/components/ui/did-you-mean";
import { useSearch } from "@/hooks/use-search";
import {
  ChevronRight,
  FileText,
  Search,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useRef, useState } from "react";
import HeroImage from "../public/images/lol-heroes-new.png";

const popularSearches = [
  "Jinx",
  "<PERSON><PERSON><PERSON>",
  "<PERSON><PERSON>",
  "<PERSON><PERSON>",
  "<PERSON><PERSON>",
  "Seraph<PERSON>",
  "Thresh",
  "Katarina",
];

export default function HomePage() {
  const [showSearchDropdown, setShowSearchDropdown] = useState(false);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const search = useSearch();



  const handlePopularSearchClick = async (search: string) => {
    // Convert champion name to proper slug and redirect
    const { getChampionSlugSync } = await import('@/lib/utils/champion-slug');
    const championSlug = getChampionSlugSync(search);
    window.location.href = `/champions/${championSlug}`;
  };

  const getTagColor = (tag: string) => {
    switch (tag) {
      case "Champions":
        return "bg-blue-600/20 text-blue-300 border-blue-500/30";
      case "Items":
        return "bg-green-600/20 text-green-300 border-green-500/30";
      case "Skins":
        return "bg-purple-600/20 text-purple-300 border-purple-500/30";
      case "Chromas":
        return "bg-pink-600/20 text-pink-300 border-pink-500/30";
      default:
        return "bg-gray-600/20 text-gray-300 border-gray-500/30";
    }
  };

  const handleSearchFocus = () => {
    setShowSearchDropdown(true);
    // Fetch random suggestions if we don't have any and no search query
    if (!search.hasQuery && search.randomSuggestions.length === 0) {
      search.fetchRandomSuggestions();
    }
  };

  // Custom suggestion click handler that keeps dropdown open
  const handleSuggestionClick = (suggestion: string) => {
    search.handleSuggestionClick(suggestion);
    // Keep dropdown open to show new results
    setShowSearchDropdown(true);
    // Focus back on input to maintain user experience
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  };

  return (
    <SharedLayout>
      {/* Hero Section */}

      <section className="relative overflow-hidden">
        {/* Hero Image - Taking the entire right side */}
        <div className="absolute hidden xl:block top-0 right-0 w-full max-w-[956px] aspect-[239/180]">
          <Image
            src={HeroImage}
            alt="League of Legends Champions"
            fill
            priority
            className="object-cover object-center"
          />
        </div>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12 lg:py-16 w-full relative">
          <div className="grid grid-cols-1 xl:grid-cols-2 gap-8 lg:gap-12 items-center">
            {/* Left Side - Text and Search */}
            <div className="space-y-6 lg:space-y-8 relative order-2 lg:order-1">
              <div>
                <h1 className="text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-bold text-white mb-4 lg:mb-6 drop-shadow-lg">
                  Your Ultimate
                  <span className="block text-transparent bg-clip-text bg-gradient-to-r from-orange-400 via-amber-400 to-yellow-400 leading-snug drop-shadow-lg">
                    League Database
                  </span>
                </h1>
                <p className="text-lg sm:text-xl text-gray-300 leading-relaxed max-w-lg drop-shadow-lg">
                  Discover champions, explore skins, track the shop, and stay
                  updated with everything League of Legends.
                </p>
              </div>

              <div className="space-y-4 lg:space-y-6">
                <div className="relative group">
                  <Search className="absolute left-3 lg:left-4 top-1/2 transform -translate-y-1/2 h-4 w-4 lg:h-5 lg:w-5 text-gray-400 group-focus-within:text-orange-400 transition-colors z-10" />
                  <Input
                    ref={searchInputRef}
                    type="text"
                    placeholder="Search champions, skins, items..."
                    value={search.searchQuery}
                    onChange={(e) => search.setSearchQuery(e.target.value)}
                    onFocus={handleSearchFocus}
                    onBlur={() => {
                      setTimeout(() => setShowSearchDropdown(false), 200);
                    }}
                    className="pl-10 lg:pl-12 pr-4 lg:pr-6 py-3 lg:py-4 text-base lg:text-lg bg-gray-900/80 border-gray-700/50 text-white placeholder-gray-400
                     focus:border-orange-400/60 focus:ring-2 focus:ring-orange-400/20 focus:bg-gray-900/90
                     hover:border-gray-600/60 transition-all duration-300 rounded-xl
                     focus:outline-none focus:shadow-lg focus:shadow-orange-400/10"
                  />

                  {/* Search Dropdown - positioned relative to input */}
                  {showSearchDropdown && (
                    <div className="absolute top-full left-0 right-0 mt-2 bg-gray-900/95 border border-orange-600/30 rounded-xl backdrop-blur-md shadow-xl overflow-hidden animate-in slide-in-from-top-2 duration-200 z-30">
                      {/* Fixed height container to prevent layout shift */}
                      <div className="h-[240px] overflow-y-auto scrollbar-thin scrollbar-thumb-orange-400/20 scrollbar-track-gray-800/20">
                        <div className="p-2 h-full">
                          {search.loading || search.isSearching ? (
                            <div className="flex items-center justify-center h-full">
                              <div className="text-center">
                                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-orange-400 mx-auto mb-2"></div>
                                <p className="text-gray-400 text-sm">
                                  Searching...
                                </p>
                              </div>
                            </div>
                          ) : search.currentSuggestions.length > 0 ? (
                            <div className="space-y-1">
                              {search.currentSuggestions.map((suggestion) => (
                                <Link
                                  key={`${suggestion.type}-${suggestion.id}`}
                                  href={suggestion.href}
                                  className="flex items-center p-3 hover:bg-gray-800/50 rounded-lg transition-all duration-150 group/item"
                                  onMouseDown={(e) => e.preventDefault()}
                                >
                                  <div className="w-10 h-10 lg:w-12 lg:h-12 mr-3 rounded-lg overflow-hidden bg-gray-800/50 flex-shrink-0">
                                    <Image
                                      src={suggestion.image}
                                      alt={suggestion.name}
                                      width={48}
                                      height={48}
                                      className="w-full h-full object-cover group-hover/item:scale-105 transition-transform duration-200"
                                    />
                                  </div>
                                  <div className="flex-1 min-w-0">
                                    <div className="text-white font-medium text-sm mb-1 group-hover/item:text-orange-300 transition-colors">
                                      {suggestion.name}
                                    </div>
                                    <div className="flex items-center">
                                      <span
                                        className={`inline-flex items-center px-2 py-0.5 rounded-md text-xs font-medium border ${getTagColor(
                                          suggestion.tag
                                        )}`}
                                      >
                                        {suggestion.tag}
                                      </span>
                                    </div>
                                  </div>
                                  <ChevronRight className="h-4 w-4 text-gray-500 transition-all duration-150 flex-shrink-0 group-hover/item:text-orange-400 group-hover/item:translate-x-1" />
                                </Link>
                              ))}
                            </div>
                          ) : search.searchQuery.trim() ? (
                            // No results found - show suggestions or sad bee emote
                            <div className="h-full">
                              {search.showSuggestions ? (
                                <div className="p-2">
                                  <div className="flex items-center justify-center mb-4">
                                    <div className="text-center">
                                      <div className="relative mx-auto mb-3 w-12 h-12">
                                        <Image
                                          src="https://raw.communitydragon.org/latest/game/assets/loadouts/summoneremotes/flairs/em_bee_sad_glow.png"
                                          alt="No results glow"
                                          width={48}
                                          height={48}
                                          className="absolute inset-0 opacity-60"
                                          unoptimized
                                        />
                                        <Image
                                          src="https://raw.communitydragon.org/latest/game/assets/loadouts/summoneremotes/flairs/em_bee_sad_vfx.png"
                                          alt="No results"
                                          width={48}
                                          height={48}
                                          className="relative z-10"
                                          unoptimized
                                        />
                                      </div>
                                      <p className="text-gray-400 text-xs">
                                        No exact matches found
                                      </p>
                                    </div>
                                  </div>
                                  <DidYouMean
                                    suggestions={search.searchSuggestions}
                                    onSuggestionClick={handleSuggestionClick}
                                    variant="orange"
                                  />
                                </div>
                              ) : (
                                <div className="flex items-center justify-center h-full">
                                  <div className="text-center">
                                    <div className="relative mx-auto mb-4 w-16 h-16">
                                      <Image
                                        src="https://raw.communitydragon.org/latest/game/assets/loadouts/summoneremotes/flairs/em_bee_sad_glow.png"
                                        alt="No results glow"
                                        width={64}
                                        height={64}
                                        className="absolute inset-0 opacity-60"
                                        unoptimized
                                      />
                                      <Image
                                        src="https://raw.communitydragon.org/latest/game/assets/loadouts/summoneremotes/flairs/em_bee_sad_vfx.png"
                                        alt="No results"
                                        width={64}
                                        height={64}
                                        className="relative z-10"
                                        unoptimized
                                      />
                                    </div>
                                    <h3 className="text-base font-semibold text-white mb-2">
                                      No results found
                                    </h3>
                                    <p className="text-gray-400 text-sm">
                                      Try searching for champions, skins, items, or
                                      chromas
                                    </p>
                                  </div>
                                </div>
                              )}
                            </div>
                          ) : (
                            // Default state - show click to see suggestions
                            <div className="flex items-center justify-center h-full">
                              <div className="text-center text-gray-400">
                                <Search className="h-6 w-6 mx-auto mb-2 opacity-50" />
                                <p className="text-sm">
                                  Click to see suggestions
                                </p>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                <div>
                  <p className="text-sm text-gray-400 mb-3 font-medium">
                    Popular Searches:
                  </p>
                  <div className="flex flex-wrap gap-2">
                    {popularSearches.map((search, index) => (
                      <Button
                        key={index}
                        variant="outline"
                        size="sm"
                        className="border-orange-700/30 text-orange-300 hover:bg-orange-800/30 hover:text-amber-400 hover:border-amber-400/50 transition-all duration-200 text-xs sm:text-sm"
                        onClick={() => handlePopularSearchClick(search)}
                      >
                        {search}
                      </Button>
                    ))}
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4 lg:gap-6 pt-6 lg:pt-8">
                <div className="text-center">
                  <div className="text-2xl lg:text-3xl font-bold text-orange-400 mb-1">
                    170
                  </div>
                  <div className="text-xs lg:text-sm text-gray-400">
                    Champions
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-2xl lg:text-3xl font-bold text-amber-400 mb-1">
                    1,800+
                  </div>
                  <div className="text-xs lg:text-sm text-gray-400">Skins</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl lg:text-3xl font-bold text-yellow-400 mb-1">
                    Daily
                  </div>
                  <div className="text-xs lg:text-sm text-gray-400">
                    Updates
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Content */}
      <section className="py-8 sm:py-12 lg:py-16 bg-gray-950/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-2xl sm:text-3xl font-bold text-white mb-6 lg:mb-8 text-center">
            Our <span className="text-orange-400">Services</span>
          </h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8">
            {/* Free Champion Rotation */}
            <Link href="/champions/free-rotation" className="block">
              <Card className="bg-gray-900/50 border-2 border-gray-700/30 hover:border-orange-400/50 hover:bg-gray-800/60 transition-all duration-300 group overflow-hidden cursor-pointer">
                <CardContent className="p-0">
                  {/* Top Icon Section */}
                  <div className="relative p-8 text-center border-b border-gray-700/30 overflow-hidden">
                    {/* Background Image with Blur */}
                    <div className="absolute inset-0 bg-cover bg-center bg-no-repeat blur-sm scale-110"
                         style={{backgroundImage: 'url(/images/FreeChampionRotationCards.jpg)'}}></div>
                    {/* Overlay for better text readability */}
                    <div className="backdrop-blur-sm backdrop-saturate-150 absolute inset-0 bg-gradient-to-r from-gray-900/60 to-gray-900/60"></div>

                    {/* Content */}
                    <div className="relative z-10">
                      <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-orange-500/30 border-2 border-orange-400/50 mb-4 group-hover:scale-110 group-hover:bg-orange-500/40 transition-all duration-300 backdrop-blur-sm">
                        <ChampionsIcon className="text-orange-300" />
                      </div>
                      <div className="text-3xl font-bold text-white mb-1 drop-shadow-lg">20</div>
                      <div className="text-sm text-orange-200 uppercase tracking-wider font-medium drop-shadow-md">Champions Available</div>
                    </div>
                  </div>

                  {/* Content Section */}
                  <div className="p-6 text-center">
                    <h3 className="text-xl font-bold text-white mb-3 group-hover:text-orange-300 transition-colors duration-300">
                      Free Champion Rotation
                    </h3>
                    <p className="text-gray-400 text-sm leading-relaxed mb-6">
                      Discover this week's free champions and expand your gameplay experience with new strategies
                    </p>
                    <Button
                      className="w-full bg-gradient-to-r from-orange-600 to-amber-600 hover:from-orange-500 hover:to-amber-500 text-white font-semibold py-3 rounded-lg transition-all duration-300 hover:shadow-xl hover:shadow-orange-500/25 group-hover:scale-105"
                      onClick={(e) => e.stopPropagation()}
                    >
                      View Rotation
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </Link>

            {/* Skin Sales */}
            <Link href="/shop/discounts" className="block">
              <Card className="bg-gray-900/50 border-2 border-gray-700/30 hover:border-amber-400/50 hover:bg-gray-800/60 transition-all duration-300 group overflow-hidden cursor-pointer">
                <CardContent className="p-0">
                  {/* Top Icon Section */}
                  <div className="relative p-8 text-center border-b border-gray-700/30 overflow-hidden">
                    {/* Background Image with Blur */}
                    <div className="absolute inset-0 bg-cover bg-center bg-no-repeat blur-sm scale-110"
                         style={{backgroundImage: 'url(/images/PatchNotesCards.jpg)'}}></div>
                    {/* Overlay for better text readability */}
                    <div className="backdrop-blur-sm backdrop-saturate-150 absolute inset-0 bg-gradient-to-r from-gray-900/60 to-gray-900/60"></div>

                    {/* Content */}
                    <div className="relative z-10">
                      <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-amber-500/30 border-2 border-amber-400/50 mb-4 group-hover:scale-110 group-hover:bg-amber-500/40 transition-all duration-300 backdrop-blur-sm">
                        <SkinsIcon className="text-amber-300" size={20} />
                      </div>
                      <div className="text-3xl font-bold text-white mb-1 drop-shadow-lg">60%</div>
                      <div className="text-sm text-amber-200 uppercase tracking-wider font-medium drop-shadow-md">Maximum Discount</div>
                    </div>
                  </div>

                  {/* Content Section */}
                  <div className="p-6 text-center">
                    <h3 className="text-xl font-bold text-white mb-3 group-hover:text-amber-300 transition-colors duration-300">
                      Skin Sales
                    </h3>
                    <p className="text-gray-400 text-sm leading-relaxed mb-6">
                      Get amazing discounts on premium skins and exclusive collections from your favorite champions
                    </p>
                    <Button
                      className="w-full bg-gradient-to-r from-amber-600 to-orange-600 hover:from-amber-500 hover:to-orange-500 text-white font-semibold py-3 rounded-lg transition-all duration-300 hover:shadow-xl hover:shadow-amber-500/25 group-hover:scale-105"
                      onClick={(e) => e.stopPropagation()}
                    >
                      Browse Sales
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </Link>

            {/* Patch Notes */}
            <Link href="/conversion-tool" className="block sm:col-span-2 lg:col-span-1">
              <Card className="bg-gray-900/50 border-2 border-gray-700/30 hover:border-yellow-400/50 hover:bg-gray-800/60 transition-all duration-300 group overflow-hidden cursor-pointer">
                <CardContent className="p-0">
                  {/* Top Icon Section */}
                  <div className="relative p-8 text-center border-b border-gray-700/30 overflow-hidden">
                    {/* Background Image with Blur */}
                    <div className="absolute inset-0 bg-cover bg-center bg-no-repeat blur-sm scale-110"
                         style={{backgroundImage: 'url(/images/SkinSalesCards.jpg)'}}></div>
                    {/* Overlay for better text readability */}
                    <div className="backdrop-blur-sm backdrop-saturate-150 absolute inset-0 bg-gradient-to-r from-gray-900/60 to-gray-900/60"></div>

                    {/* Content */}
                    <div className="relative z-10">
                      <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-yellow-500/30 border-2 border-yellow-400/50 mb-4 group-hover:scale-110 group-hover:bg-yellow-500/40 transition-all duration-300 backdrop-blur-sm">
                        <Image
                          src="/CurrencyConversion.svg"
                          alt="Currency Conversion"
                          width={20}
                          height={20}
                          className="w-5 h-5"
                        />
                      </div>
                      <div className="text-3xl font-bold text-white mb-1 drop-shadow-lg">Convert</div>
                      <div className="text-sm text-yellow-200 uppercase tracking-wider font-medium drop-shadow-md">In-Game Currencies</div>
                    </div>
                  </div>

                  {/* Content Section */}
                  <div className="p-6 text-center">
                    <h3 className="text-xl font-bold text-white mb-3 group-hover:text-yellow-300 transition-colors duration-300">
                      Conversion Tool
                    </h3>
                    <p className="text-gray-400 text-sm leading-relaxed mb-6">
                      Convert between League of Legends currencies and real money with live exchange rates
                    </p>
                    <Button
                      className="w-full bg-gradient-to-r from-yellow-600 to-orange-600 hover:from-yellow-500 hover:to-orange-500 text-white font-semibold py-3 rounded-lg transition-all duration-300 hover:shadow-xl hover:shadow-yellow-500/25 group-hover:scale-105"
                      onClick={(e) => e.stopPropagation()}
                    >
                      Riot Points to USD
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </Link>

            {/* Latest Skins */}
            <Link href="/skins?sort=release-desc" className="block">
              <Card className="bg-gray-900/50 border-2 border-gray-700/30 hover:border-purple-400/50 hover:bg-gray-800/60 transition-all duration-300 group overflow-hidden cursor-pointer">
                <CardContent className="p-0">
                  {/* Top Icon Section */}
                  <div className="relative p-8 text-center border-b border-gray-700/30 overflow-hidden">
                    {/* Background Image with Blur */}
                    <div className="absolute inset-0 bg-cover bg-center bg-no-repeat blur-sm scale-110"
                         style={{backgroundImage: 'url(/images/LatestSkinsCards.jpg)'}}></div>
                    {/* Overlay for better text readability */}
                    <div className="backdrop-blur-sm backdrop-saturate-150 absolute inset-0 bg-gradient-to-r from-gray-900/60 to-gray-900/60"></div>

                    {/* Content */}
                    <div className="relative z-10">
                      <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-purple-500/30 border-2 border-purple-400/50 mb-4 group-hover:scale-110 group-hover:bg-purple-500/40 transition-all duration-300 backdrop-blur-sm">
                        <SkinsIcon className="text-purple-300" size={20} />
                      </div>
                      <div className="text-3xl font-bold text-white mb-1 drop-shadow-lg">New</div>
                      <div className="text-sm text-purple-200 uppercase tracking-wider font-medium drop-shadow-md">Fresh Releases</div>
                    </div>
                  </div>

                  {/* Content Section */}
                  <div className="p-6 text-center">
                    <h3 className="text-xl font-bold text-white mb-3 group-hover:text-purple-300 transition-colors duration-300">
                      Latest Skins
                    </h3>
                    <p className="text-gray-400 text-sm leading-relaxed mb-6">
                      Explore the newest skin releases and upcoming cosmetic previews for all champions
                    </p>
                    <Button
                      className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-500 hover:to-pink-500 text-white font-semibold py-3 rounded-lg transition-all duration-300 hover:shadow-xl hover:shadow-purple-500/25 group-hover:scale-105"
                      onClick={(e) => e.stopPropagation()}
                    >
                      View Latest Skins
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </Link>

            {/* Mythic Shop */}
            <Link href="/shop/mythic" className="block">
              <Card className="bg-gray-900/50 border-2 border-gray-700/30 hover:border-purple-400/50 hover:bg-gray-800/60 transition-all duration-300 group overflow-hidden cursor-pointer">
                <CardContent className="p-0">
                  {/* Top Icon Section */}
                  <div className="relative p-8 text-center border-b border-gray-700/30 overflow-hidden">
                    {/* Background Image with Blur */}
                    <div className="absolute inset-0 bg-cover bg-center bg-no-repeat blur-sm scale-110"
                         style={{backgroundImage: 'url(/images/MythicShopCards.jpg)'}}></div>
                    {/* Overlay for better text readability */}
                    <div className="backdrop-blur-sm backdrop-saturate-150 absolute inset-0 bg-gradient-to-r from-gray-900/60 to-gray-900/60"></div>

                    {/* Content */}
                    <div className="relative z-10">
                      <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-purple-500/30 border-2 border-purple-400/50 mb-4 group-hover:scale-110 group-hover:bg-purple-500/40 transition-all duration-300 backdrop-blur-sm">
                        <Image
                          src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/mythic.svg"
                          alt="Mythic Essence"
                          width={20}
                          height={20}
                          className="w-5 h-5"
                        />
                      </div>
                      <div className="text-3xl font-bold text-white mb-1 drop-shadow-lg">Mythic Shop</div>
                      <div className="text-sm text-purple-200 uppercase tracking-wider font-medium drop-shadow-md">Mythic Essence</div>
                    </div>
                  </div>

                  {/* Content Section */}
                  <div className="p-6 text-center">
                    <h3 className="text-xl font-bold text-white mb-3 group-hover:text-purple-300 transition-colors duration-300">
                      Mythic Shop Rotation
                    </h3>
                    <p className="text-gray-400 text-sm leading-relaxed mb-6">
                      Exclusive mythic skins and prestige content available for Mythic Essence
                    </p>
                    <Button
                      className="w-full bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-500 hover:to-indigo-500 text-white font-semibold py-3 rounded-lg transition-all duration-300 hover:shadow-xl hover:shadow-purple-500/25 group-hover:scale-105"
                      onClick={(e) => e.stopPropagation()}
                    >
                      Browse Mythic Shop
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </Link>

            {/* Shop */}
            <Link href="/shop" className="block">
              <Card className="bg-gray-900/50 border-2 border-gray-700/30 hover:border-green-400/50 hover:bg-gray-800/60 transition-all duration-300 group overflow-hidden cursor-pointer">
                <CardContent className="p-0">
                  {/* Top Icon Section */}
                  <div className="relative p-8 text-center border-b border-gray-700/30 overflow-hidden">
                    {/* Background Image with Blur */}
                    <div className="absolute inset-0 bg-cover bg-center bg-no-repeat blur-sm scale-110"
                         style={{backgroundImage: 'url(/images/CurrentStoreCards.jpg)'}}></div>
                    {/* Overlay for better text readability */}
                    <div className="backdrop-blur-sm backdrop-saturate-150 absolute inset-0 bg-gradient-to-r from-gray-900/60 to-gray-900/60"></div>

                    {/* Content */}
                    <div className="relative z-10">
                      <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-green-500/30 border-2 border-green-400/50 mb-4 group-hover:scale-110 group-hover:bg-green-500/40 transition-all duration-300 backdrop-blur-sm">
                        <Image
                          src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/rp.svg"
                          alt="Riot Points"
                          width={20}
                          height={20}
                          className="w-5 h-5"
                          style={{filter: 'brightness(0) saturate(100%) invert(64%) sepia(98%) saturate(1000%) hue-rotate(90deg) brightness(110%) contrast(110%)'}}
                        />
                      </div>
                      <div className="text-3xl font-bold text-white mb-1 drop-shadow-lg">Store</div>
                      <div className="text-sm text-green-200 uppercase tracking-wider font-medium drop-shadow-md">Riot Points</div>
                    </div>
                  </div>

                  {/* Content Section */}
                  <div className="p-6 text-center">
                    <h3 className="text-xl font-bold text-white mb-3 group-hover:text-green-300 transition-colors duration-300">
                      Current Store
                    </h3>
                    <p className="text-gray-400 text-sm leading-relaxed mb-6">
                      Browse the current featured items, bundles, and rotating shop content
                    </p>
                    <Button
                      className="w-full bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-500 hover:to-emerald-500 text-white font-semibold py-3 rounded-lg transition-all duration-300 hover:shadow-xl hover:shadow-green-500/25 group-hover:scale-105"
                      onClick={(e) => e.stopPropagation()}
                    >
                      Visit Shop
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </Link>
          </div>
        </div>
      </section>

      {/* Latest Blogs */}
      <section className="py-8 sm:py-12 lg:py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-2xl sm:text-3xl font-bold text-white mb-6 lg:mb-8 text-center">
            Latest <span className="text-orange-400">Blogs</span>
          </h2>
          <div className="flex justify-center">
            <Card className="bg-gray-900/40 border-gray-700/20 max-w-md">
              <CardContent className="p-8 text-center">
                <div className="text-gray-400 mb-4">
                  <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                </div>
                <h3 className="text-lg font-semibold text-white mb-2">No Recent Blogs to Show</h3>
                <p className="text-gray-400 text-sm">
                  We're working on bringing you the latest League of Legends content. Check back soon!
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
    </SharedLayout>
  );
}
